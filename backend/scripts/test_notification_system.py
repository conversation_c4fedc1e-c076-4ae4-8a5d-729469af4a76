#!/usr/bin/env python3
"""
Test Notification System - Sends notifications every 2 minutes

This script is designed for testing the notification pipeline with real users.
It bypasses the normal timezone-based scheduling and sends notifications every 2 minutes
to validate that the entire system works end-to-end.

WARNING: This will send actual notifications to real users. Use with caution!

Usage:
    python3 test_notification_system.py [--dry-run] [--max-users N]

Options:
    --dry-run: Show what would be sent without actually sending notifications
    --max-users N: Limit notifications to first N users (default: 5 for safety)
"""

import asyncio
import json
import os
import sys
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path
import time

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("ℹ python-dotenv not available, using system environment variables")

import firebase_admin
import pytz

# Import notification components
from utils.notifications import send_notification, send_bulk_notification
from database.notifications import get_users_token_in_timezones, get_users_id_in_timezones
from utils.llm.external_integrations import get_conversation_summary
from models.notification_message import NotificationMessage
from utils.webhooks import day_summary_webhook
import database.chat as chat_db
import database.conversations as conversations_db
import threading


class TestNotificationSystem:
    def __init__(self, dry_run=False, max_users=5):
        self.dry_run = dry_run
        self.max_users = max_users
        self.logger = self.setup_logging()
        self.firebase_initialized = False
        self.notification_count = 0
        self.start_time = datetime.now()
        
    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('/tmp/test_notifications.log', mode='a')
            ]
        )
        return logging.getLogger('TestNotificationSystem')
    
    def initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            # Check if Firebase is already initialized
            firebase_admin.get_app()
            self.logger.info("Firebase already initialized")
            self.firebase_initialized = True
            return True
        except ValueError:
            pass
        
        try:
            if os.environ.get('SERVICE_ACCOUNT_JSON'):
                service_account_info = json.loads(os.environ["SERVICE_ACCOUNT_JSON"])
                credentials = firebase_admin.credentials.Certificate(service_account_info)
                firebase_admin.initialize_app(credentials)
                self.logger.info("Firebase initialized with SERVICE_ACCOUNT_JSON")
                self.firebase_initialized = True
                return True
            else:
                firebase_admin.initialize_app()
                self.logger.info("Firebase initialized with default credentials")
                self.firebase_initialized = True
                return True
        except Exception as e:
            self.logger.error(f"Failed to initialize Firebase: {e}")
            return False
    
    async def get_test_users(self):
        """Get a limited set of users for testing"""
        try:
            # Get users from a few major timezones to ensure we have some users
            test_timezones = [
                'America/New_York', 'America/Los_Angeles', 'Europe/London', 
                'Europe/Paris', 'Asia/Tokyo', 'Asia/Shanghai', 'Australia/Sydney',
                'America/Chicago', 'Europe/Berlin', 'Asia/Mumbai'
            ]
            
            self.logger.info(f"Querying users from {len(test_timezones)} test timezones...")
            all_users = await get_users_token_in_timezones(test_timezones)
            
            # Limit users for safety
            limited_users = all_users[:self.max_users] if all_users else []
            
            self.logger.info(f"Found {len(all_users)} total users, using {len(limited_users)} for testing")
            return limited_users
            
        except Exception as e:
            self.logger.error(f"Error getting test users: {e}")
            return []
    
    async def get_test_users_for_summary(self):
        """Get users with IDs for summary notifications"""
        try:
            test_timezones = [
                'America/New_York', 'America/Los_Angeles', 'Europe/London', 
                'Europe/Paris', 'Asia/Tokyo', 'Asia/Shanghai', 'Australia/Sydney',
                'America/Chicago', 'Europe/Berlin', 'Asia/Mumbai'
            ]
            
            all_users = await get_users_id_in_timezones(test_timezones)
            limited_users = all_users[:self.max_users] if all_users else []
            
            self.logger.info(f"Found {len(all_users)} total users for summaries, using {len(limited_users)} for testing")
            return limited_users
            
        except Exception as e:
            self.logger.error(f"Error getting test users for summary: {e}")
            return []
    
    async def send_test_morning_notification(self):
        """Send morning reminder notifications to test users"""
        try:
            self.logger.info("=== Sending Test Morning Notifications ===")
            
            users = await self.get_test_users()
            if not users:
                self.logger.warning("No users found for morning notifications")
                return
            
            title = "Memorion (TEST)"
            body = "TEST: Wear your Memorion device to capture your conversations today."
            
            if self.dry_run:
                self.logger.info(f"DRY RUN: Would send morning notification to {len(users)} users")
                self.logger.info(f"Title: {title}")
                self.logger.info(f"Body: {body}")
                return
            
            self.logger.info(f"Sending morning notifications to {len(users)} users...")
            await send_bulk_notification(users, title, body)
            self.notification_count += len(users)
            self.logger.info(f"✓ Morning notifications sent to {len(users)} users")
            
        except Exception as e:
            self.logger.error(f"Error sending morning notifications: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    def send_summary_notification_to_user(self, user_data: tuple):
        """Send summary notification to a single user"""
        try:
            uid = user_data[0]
            fcm_token = user_data[1]
            
            title = "Here is your action plan for tomorrow (TEST)"
            
            # Try to get actual conversations for summary
            from datetime import time
            memories = conversations_db.filter_conversations_by_date(
                uid, datetime.combine(datetime.now().date(), time.min), datetime.now()
            )
            
            if memories:
                try:
                    summary = get_conversation_summary(uid, memories)
                    body = f"TEST SUMMARY: {summary[:200]}..." if len(summary) > 200 else f"TEST SUMMARY: {summary}"
                except Exception as e:
                    self.logger.warning(f"Could not generate summary for user {uid}: {e}")
                    body = "TEST: Your daily conversation summary is being prepared. Check back later for insights from today's conversations."
            else:
                body = "TEST: No conversations found today. Start wearing your Memorion device to capture meaningful moments!"
            
            if self.dry_run:
                self.logger.info(f"DRY RUN: Would send summary to user {uid}")
                self.logger.info(f"Title: {title}")
                self.logger.info(f"Body: {body[:100]}...")
                return
            
            # Create notification message
            ai_message = NotificationMessage(
                text=body,
                from_integration='false',
                type='day_summary',
                notification_type='daily_summary',
                navigate_to="/chat/omi",
            )
            
            # Send notification
            send_notification(fcm_token, title, body, NotificationMessage.get_message_as_dict(ai_message))
            
            # Add summary message to chat (optional for testing)
            try:
                chat_db.add_summary_message(body, uid)
            except Exception as e:
                self.logger.warning(f"Could not add summary message for user {uid}: {e}")
            
            # Trigger webhook (optional for testing)
            try:
                threading.Thread(target=day_summary_webhook, args=(uid, body)).start()
            except Exception as e:
                self.logger.warning(f"Could not trigger webhook for user {uid}: {e}")
                
            self.logger.info(f"✓ Summary notification sent to user {uid}")
            
        except Exception as e:
            self.logger.error(f"Error sending summary notification to user {user_data}: {e}")
    
    async def send_test_summary_notifications(self):
        """Send evening summary notifications to test users"""
        try:
            self.logger.info("=== Sending Test Summary Notifications ===")
            
            users = await self.get_test_users_for_summary()
            if not users:
                self.logger.warning("No users found for summary notifications")
                return
            
            if self.dry_run:
                self.logger.info(f"DRY RUN: Would send summary notifications to {len(users)} users")
                return
            
            self.logger.info(f"Sending summary notifications to {len(users)} users...")
            
            # Send to each user individually (summaries are personalized)
            import concurrent.futures
            loop = asyncio.get_running_loop()
            with concurrent.futures.ThreadPoolExecutor() as pool:
                tasks = [
                    loop.run_in_executor(pool, self.send_summary_notification_to_user, user) 
                    for user in users
                ]
                await asyncio.gather(*tasks)
            
            self.notification_count += len(users)
            self.logger.info(f"✓ Summary notifications sent to {len(users)} users")
            
        except Exception as e:
            self.logger.error(f"Error sending summary notifications: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
    
    async def run_test_cycle(self):
        """Run one test cycle (both morning and summary notifications)"""
        cycle_start = datetime.now()
        self.logger.info(f"🔄 Starting test cycle at {cycle_start}")
        
        # Send morning notification
        await self.send_test_morning_notification()
        
        # Wait 30 seconds between notification types
        await asyncio.sleep(30)
        
        # Send summary notification
        await self.send_test_summary_notifications()
        
        cycle_end = datetime.now()
        cycle_duration = (cycle_end - cycle_start).total_seconds()
        
        self.logger.info(f"✅ Test cycle completed in {cycle_duration:.1f} seconds")
        self.logger.info(f"📊 Total notifications sent so far: {self.notification_count}")
        
    async def run_continuous_test(self, duration_minutes=10):
        """Run continuous testing for specified duration"""
        self.logger.info("🚀 Starting Continuous Notification Testing")
        self.logger.info(f"⏱️  Test duration: {duration_minutes} minutes")
        self.logger.info(f"🔄 Notification cycle: Every 2 minutes")
        self.logger.info(f"👥 Max users per cycle: {self.max_users}")
        self.logger.info(f"🧪 Dry run mode: {'ON' if self.dry_run else 'OFF'}")
        
        if not self.dry_run:
            self.logger.warning("⚠️  REAL NOTIFICATIONS WILL BE SENT TO USERS!")
            self.logger.warning("⚠️  Press Ctrl+C to stop at any time")
        
        end_time = datetime.now() + timedelta(minutes=duration_minutes)
        cycle_count = 0
        
        try:
            while datetime.now() < end_time:
                cycle_count += 1
                self.logger.info(f"📋 Cycle {cycle_count} of approximately {duration_minutes//2}")
                
                await self.run_test_cycle()
                
                # Wait 2 minutes before next cycle
                self.logger.info("⏳ Waiting 2 minutes before next cycle...")
                for i in range(120):  # 120 seconds = 2 minutes
                    if datetime.now() >= end_time:
                        break
                    await asyncio.sleep(1)
                
                if datetime.now() >= end_time:
                    break
                    
        except KeyboardInterrupt:
            self.logger.info("🛑 Test interrupted by user")
        
        # Final summary
        total_duration = (datetime.now() - self.start_time).total_seconds() / 60
        self.logger.info("🏁 Test Summary:")
        self.logger.info(f"   Duration: {total_duration:.1f} minutes")
        self.logger.info(f"   Cycles completed: {cycle_count}")
        self.logger.info(f"   Total notifications: {self.notification_count}")
        self.logger.info(f"   Dry run mode: {'ON' if self.dry_run else 'OFF'}")


async def main():
    parser = argparse.ArgumentParser(description='Test OMI Notification System with Real Users')
    parser.add_argument('--dry-run', action='store_true', 
                       help='Show what would be sent without actually sending notifications')
    parser.add_argument('--max-users', type=int, default=5,
                       help='Maximum number of users to send notifications to (default: 5)')
    parser.add_argument('--duration', type=int, default=10,
                       help='Test duration in minutes (default: 10)')
    
    args = parser.parse_args()
    
    # Safety check
    if not args.dry_run and args.max_users > 10:
        print("⚠️  WARNING: You're about to send notifications to more than 10 users!")
        print("⚠️  This could be disruptive. Consider using --dry-run first.")
        response = input("Continue? (yes/no): ")
        if response.lower() != 'yes':
            print("Test cancelled.")
            return
    
    test_system = TestNotificationSystem(dry_run=args.dry_run, max_users=args.max_users)
    
    # Initialize Firebase
    if not test_system.initialize_firebase():
        print("❌ Failed to initialize Firebase. Exiting.")
        sys.exit(1)
    
    # Run the test
    await test_system.run_continuous_test(duration_minutes=args.duration)


if __name__ == "__main__":
    asyncio.run(main())
