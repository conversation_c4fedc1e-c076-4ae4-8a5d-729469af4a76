#!/usr/bin/env python3
"""
Notification Mode Switcher

This script helps switch between normal notification scheduling and test mode.
It creates backup copies and modifies the notification logic temporarily.

Usage:
    python3 notification_mode_switcher.py [enable-test|disable-test|status]
"""

import os
import sys
import shutil
from pathlib import Path
from datetime import datetime

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

NOTIFICATIONS_FILE = backend_dir / "utils" / "other" / "notifications.py"
BACKUP_FILE = backend_dir / "utils" / "other" / "notifications.py.backup"
TEST_PATCH_FILE = backend_dir / "scripts" / "test_mode_patch.py"


def create_test_patch():
    """Create the test mode patch for notifications.py"""
    patch_content = '''
# TEST MODE PATCH - This modifies should_run_job to always return True for testing

def should_run_job():
    """TEST MODE: Always return True to send notifications every cycle"""
    current_utc = datetime.now(pytz.utc)
    print(f'TEST MODE: should_run_job called at {current_utc} - ALWAYS RETURNING TRUE')
    return True
'''
    
    with open(TEST_PATCH_FILE, 'w') as f:
        f.write(patch_content)
    
    print(f"✓ Created test patch at {TEST_PATCH_FILE}")


def backup_original():
    """Create backup of original notifications.py"""
    if not BACKUP_FILE.exists():
        shutil.copy2(NOTIFICATIONS_FILE, BACKUP_FILE)
        print(f"✓ Created backup at {BACKUP_FILE}")
    else:
        print(f"ℹ Backup already exists at {BACKUP_FILE}")


def enable_test_mode():
    """Enable test mode by modifying should_run_job function"""
    print("🧪 Enabling Test Mode...")
    
    # Create backup first
    backup_original()
    
    # Read original file
    with open(NOTIFICATIONS_FILE, 'r') as f:
        content = f.read()
    
    # Check if already in test mode
    if "TEST MODE:" in content:
        print("⚠️  Test mode is already enabled!")
        return False
    
    # Find and replace the should_run_job function
    original_function = '''def should_run_job():
    current_utc = datetime.now(pytz.utc)
    target_hours = {8, 22}
    print(f'should_run_job: Current UTC time: {current_utc}')

    matching_timezones = []
    for tz in pytz.all_timezones:
        try:
            local_time = current_utc.astimezone(pytz.timezone(tz))
            if local_time.hour in target_hours and local_time.minute == 0:
                matching_timezones.append(f"{tz} ({local_time.strftime('%H:%M')})")
        except Exception:
            # Skip invalid timezones
            continue

    if matching_timezones:
        print(f'should_run_job: Found {len(matching_timezones)} timezones at target hours: {matching_timezones[:5]}...')
        return True

    print('should_run_job: No timezones found at target hours (8 AM or 10 PM)')
    return False'''
    
    test_function = '''def should_run_job():
    """TEST MODE: Always return True to send notifications every cycle"""
    current_utc = datetime.now(pytz.utc)
    print(f'TEST MODE: should_run_job called at {current_utc} - ALWAYS RETURNING TRUE')
    print('TEST MODE: Bypassing timezone checks - notifications will be sent every cycle')
    return True'''
    
    # Replace the function
    if original_function in content:
        modified_content = content.replace(original_function, test_function)
        
        # Write modified content
        with open(NOTIFICATIONS_FILE, 'w') as f:
            f.write(modified_content)
        
        print("✅ Test mode enabled successfully!")
        print("⚠️  WARNING: Notifications will now be sent every minute regardless of timezone!")
        print("⚠️  Remember to disable test mode when done testing.")
        return True
    else:
        print("❌ Could not find the should_run_job function to modify.")
        print("The file structure may have changed. Manual modification required.")
        return False


def disable_test_mode():
    """Disable test mode by restoring original file"""
    print("🔄 Disabling Test Mode...")
    
    if not BACKUP_FILE.exists():
        print("❌ No backup file found. Cannot restore original.")
        return False
    
    # Check if currently in test mode
    with open(NOTIFICATIONS_FILE, 'r') as f:
        content = f.read()
    
    if "TEST MODE:" not in content:
        print("ℹ Test mode is not currently enabled.")
        return True
    
    # Restore from backup
    shutil.copy2(BACKUP_FILE, NOTIFICATIONS_FILE)
    print("✅ Original notification scheduling restored!")
    print("✅ Normal timezone-based scheduling is now active.")
    
    return True


def show_status():
    """Show current notification mode status"""
    print("📊 Notification System Status")
    print("=" * 40)
    
    # Check if backup exists
    if BACKUP_FILE.exists():
        backup_time = datetime.fromtimestamp(BACKUP_FILE.stat().st_mtime)
        print(f"✓ Backup file exists (created: {backup_time})")
    else:
        print("✗ No backup file found")
    
    # Check current mode
    with open(NOTIFICATIONS_FILE, 'r') as f:
        content = f.read()
    
    if "TEST MODE:" in content:
        print("🧪 Current Mode: TEST MODE")
        print("⚠️  Notifications are being sent every cycle!")
        print("⚠️  Use 'disable-test' to return to normal scheduling")
    else:
        print("✅ Current Mode: NORMAL")
        print("ℹ  Notifications follow normal timezone-based scheduling (8 AM & 10 PM)")
    
    # Show file modification time
    mod_time = datetime.fromtimestamp(NOTIFICATIONS_FILE.stat().st_mtime)
    print(f"📝 notifications.py last modified: {mod_time}")


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 notification_mode_switcher.py [enable-test|disable-test|status]")
        print()
        print("Commands:")
        print("  enable-test  - Enable test mode (notifications every cycle)")
        print("  disable-test - Disable test mode (restore normal scheduling)")
        print("  status       - Show current mode status")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "enable-test":
        success = enable_test_mode()
        if success:
            print()
            print("🚀 Next steps:")
            print("1. Use the normal cron job or service - it will now send notifications every minute")
            print("2. Monitor logs to see notifications being sent")
            print("3. Run 'python3 notification_mode_switcher.py disable-test' when done")
    
    elif command == "disable-test":
        success = disable_test_mode()
        if success:
            print()
            print("🎯 Normal scheduling restored!")
            print("Notifications will now only be sent at 8 AM and 10 PM local time.")
    
    elif command == "status":
        show_status()
    
    else:
        print(f"Unknown command: {command}")
        print("Use: enable-test, disable-test, or status")
        sys.exit(1)


if __name__ == "__main__":
    main()
