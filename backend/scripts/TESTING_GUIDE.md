# OMI Notification System - Testing Guide

This guide explains how to test the notification system with real users to validate the entire pipeline works end-to-end.

## ⚠️ Important Safety Notes

- **Real notifications will be sent to real users**
- **Start with dry-run mode and small user counts**
- **Test during appropriate hours to minimize user disruption**
- **Always revert to normal scheduling after testing**

## 🧪 Testing Options

### Option 1: Standalone Test Script (Recommended)
Use the dedicated test script that runs independently and sends notifications every 2 minutes.

### Option 2: Modified Cron/Service
Temporarily modify the existing notification system to bypass timezone checks.

## 📋 Option 1: Standalone Test Script

### Step 1: Dry Run Test
```bash
cd /home/<USER>/omi/backend

# Test with dry run (no actual notifications sent)
python3 scripts/test_notification_system.py --dry-run --max-users 3 --duration 5
```

### Step 2: Small Scale Real Test
```bash
# Send real notifications to 2 users for 4 minutes
python3 scripts/test_notification_system.py --max-users 2 --duration 4
```

### Step 3: Monitor Results
```bash
# Watch the test logs
tail -f /tmp/test_notifications.log

# Check system logs for any errors
tail -f /var/log/syslog | grep -i firebase
```

### Test Script Options
```bash
python3 scripts/test_notification_system.py [OPTIONS]

Options:
  --dry-run          Show what would be sent without sending
  --max-users N      Limit to N users (default: 5, max recommended: 10)
  --duration M       Test duration in minutes (default: 10)
```

## 🔄 Option 2: Modified Cron/Service

### Step 1: Enable Test Mode
```bash
cd /home/<USER>/omi/backend

# Check current status
python3 scripts/notification_mode_switcher.py status

# Enable test mode (bypasses timezone checks)
python3 scripts/notification_mode_switcher.py enable-test
```

### Step 2: Start the Service/Cron
```bash
# If using systemd service
./scripts/setup_notification_service.sh restart

# If using cron job, it will automatically pick up the changes
```

### Step 3: Monitor Notifications
```bash
# For systemd service
./scripts/setup_notification_service.sh logs

# For cron job
tail -f /var/log/omi_notifications.log
```

### Step 4: Disable Test Mode (IMPORTANT!)
```bash
# Restore normal scheduling
python3 scripts/notification_mode_switcher.py disable-test

# Restart service if using systemd
./scripts/setup_notification_service.sh restart
```

## 📊 Monitoring and Validation

### 1. Log Monitoring
```bash
# Test script logs
tail -f /tmp/test_notifications.log

# System notification logs
tail -f /var/log/omi_notifications.log

# System service logs (if using systemd)
journalctl -u omi-notifications -f

# Firebase/system errors
tail -f /var/log/syslog | grep -E "(firebase|notification)"
```

### 2. What to Look For

#### Successful Notifications
```
✓ Morning notifications sent to 3 users
✓ Summary notifications sent to 3 users
✓ Firebase initialized with SERVICE_ACCOUNT_JSON
```

#### Database Queries
```
Found 15 total users, using 3 for testing
Database query successful. Found 3 users in sample timezones
```

#### Firebase Responses
```
send_notification success: projects/your-project/messages/0:**********
```

### 3. Error Indicators
```
# Firebase authentication issues
Failed to initialize Firebase: ...

# Database connection issues
Database query failed: ...

# Invalid FCM tokens
Requested entity was not found

# Network issues
Error sending message: ...
```

## 🎯 Test Scenarios

### Scenario 1: Quick Validation (5 minutes)
```bash
# Dry run first
python3 scripts/test_notification_system.py --dry-run --max-users 2 --duration 2

# Real test
python3 scripts/test_notification_system.py --max-users 2 --duration 3
```

### Scenario 2: Comprehensive Test (15 minutes)
```bash
# Test with more users and longer duration
python3 scripts/test_notification_system.py --max-users 5 --duration 10
```

### Scenario 3: Production Readiness Test
```bash
# Use the modified cron/service approach
python3 scripts/notification_mode_switcher.py enable-test
# Let it run for 10-15 minutes
python3 scripts/notification_mode_switcher.py disable-test
```

## 🔍 Validation Checklist

- [ ] Dry run completes without errors
- [ ] Firebase initializes successfully
- [ ] Database queries return users
- [ ] Morning notifications are sent
- [ ] Summary notifications are sent
- [ ] FCM tokens are valid (no "entity not found" errors)
- [ ] Logs show successful Firebase responses
- [ ] Users receive notifications on their devices
- [ ] Normal scheduling is restored after testing

## 🚨 Troubleshooting

### No Users Found
```bash
# Check if users exist in database
python3 -c "
import sys; sys.path.append('.')
from database.notifications import get_users_token_in_timezones
import asyncio
users = asyncio.run(get_users_token_in_timezones(['America/New_York']))
print(f'Found {len(users)} users')
"
```

### Firebase Errors
```bash
# Verify Firebase credentials
python3 -c "
import os, json
creds = os.environ.get('SERVICE_ACCOUNT_JSON')
if creds:
    data = json.loads(creds)
    print(f'Project ID: {data.get(\"project_id\")}')
    print(f'Client Email: {data.get(\"client_email\")}')
else:
    print('No SERVICE_ACCOUNT_JSON found')
"
```

### Invalid FCM Tokens
- This is normal - some users may have uninstalled the app
- The system automatically removes invalid tokens
- Look for successful sends, not just errors

## 🔄 Reverting Changes

### After Standalone Testing
No changes needed - the test script doesn't modify the main system.

### After Modified Cron/Service Testing
```bash
# CRITICAL: Always run this after testing
python3 scripts/notification_mode_switcher.py disable-test

# Verify normal mode is restored
python3 scripts/notification_mode_switcher.py status

# Restart service if using systemd
./scripts/setup_notification_service.sh restart
```

## 📱 User Experience

### What Users Will See

#### Morning Notification
- **Title**: "Memorion (TEST)" or "Memorion"
- **Body**: "TEST: Wear your Memorion device..." or normal message

#### Summary Notification  
- **Title**: "Here is your action plan for tomorrow (TEST)"
- **Body**: Actual conversation summary or test message

### Minimizing Disruption
- Test during business hours in your timezone
- Use small user counts (2-5 users)
- Keep test duration short (5-10 minutes)
- Add "TEST" prefix to notification titles
- Inform your team about testing schedule

## 🎉 Success Criteria

Your notification system is working correctly if:

1. ✅ Test script completes without critical errors
2. ✅ Firebase authentication succeeds
3. ✅ Database queries return users
4. ✅ Notifications are sent successfully (check logs)
5. ✅ Users receive notifications on their devices
6. ✅ Invalid tokens are handled gracefully
7. ✅ System can be reverted to normal scheduling

Once validated, your notification system is ready for production use!
