#!/bin/bash

# Quick Test Runner for OMI Notification System
# This script provides easy access to common testing scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

BACKEND_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"

echo -e "${BLUE}🧪 OMI Notification System - Test Runner${NC}"
echo -e "Backend directory: ${BACKEND_DIR}"
echo ""

cd "$BACKEND_DIR"

# Function to run dry run test
run_dry_test() {
    echo -e "${YELLOW}Running dry run test (no actual notifications sent)...${NC}"
    python3 scripts/test_notification_system.py --dry-run --max-users 3 --duration 2
}

# Function to run small real test
run_small_test() {
    echo -e "${YELLOW}Running small real test (2 users, 4 minutes)...${NC}"
    echo -e "${RED}⚠️  This will send REAL notifications to users!${NC}"
    read -p "Continue? (yes/no): " confirm
    if [ "$confirm" = "yes" ]; then
        python3 scripts/test_notification_system.py --max-users 2 --duration 4
    else
        echo "Test cancelled."
    fi
}

# Function to run comprehensive test
run_comprehensive_test() {
    echo -e "${YELLOW}Running comprehensive test (5 users, 10 minutes)...${NC}"
    echo -e "${RED}⚠️  This will send REAL notifications to users for 10 minutes!${NC}"
    read -p "Continue? (yes/no): " confirm
    if [ "$confirm" = "yes" ]; then
        python3 scripts/test_notification_system.py --max-users 5 --duration 10
    else
        echo "Test cancelled."
    fi
}

# Function to enable test mode for cron/service
enable_test_mode() {
    echo -e "${YELLOW}Enabling test mode for cron/service...${NC}"
    python3 scripts/notification_mode_switcher.py enable-test
    echo ""
    echo -e "${GREEN}Test mode enabled!${NC}"
    echo -e "Your cron job or service will now send notifications every minute."
    echo -e "Monitor with: ${BLUE}tail -f /var/log/omi_notifications.log${NC}"
    echo -e "Disable with: ${BLUE}./scripts/run_notification_test.sh disable-test${NC}"
}

# Function to disable test mode
disable_test_mode() {
    echo -e "${YELLOW}Disabling test mode...${NC}"
    python3 scripts/notification_mode_switcher.py disable-test
    echo -e "${GREEN}Normal scheduling restored!${NC}"
}

# Function to show status
show_status() {
    python3 scripts/notification_mode_switcher.py status
}

# Function to show logs
show_logs() {
    echo -e "${BLUE}Recent test logs:${NC}"
    if [ -f "/tmp/test_notifications.log" ]; then
        tail -20 /tmp/test_notifications.log
    else
        echo "No test logs found."
    fi
    
    echo ""
    echo -e "${BLUE}Recent system logs:${NC}"
    if [ -f "/var/log/omi_notifications.log" ]; then
        tail -10 /var/log/omi_notifications.log
    else
        echo "No system logs found."
    fi
}

# Main menu
case "${1:-menu}" in
    "dry-run")
        run_dry_test
        ;;
    "small-test")
        run_small_test
        ;;
    "comprehensive")
        run_comprehensive_test
        ;;
    "enable-test")
        enable_test_mode
        ;;
    "disable-test")
        disable_test_mode
        ;;
    "status")
        show_status
        ;;
    "logs")
        show_logs
        ;;
    "menu"|*)
        echo -e "${BLUE}Available test commands:${NC}"
        echo ""
        echo -e "${GREEN}Standalone Testing (Recommended):${NC}"
        echo -e "  ${YELLOW}dry-run${NC}        - Test without sending notifications (safe)"
        echo -e "  ${YELLOW}small-test${NC}     - Send to 2 users for 4 minutes"
        echo -e "  ${YELLOW}comprehensive${NC}  - Send to 5 users for 10 minutes"
        echo ""
        echo -e "${GREEN}Cron/Service Testing:${NC}"
        echo -e "  ${YELLOW}enable-test${NC}    - Make cron/service send every minute"
        echo -e "  ${YELLOW}disable-test${NC}   - Restore normal scheduling"
        echo -e "  ${YELLOW}status${NC}         - Show current mode"
        echo ""
        echo -e "${GREEN}Monitoring:${NC}"
        echo -e "  ${YELLOW}logs${NC}           - Show recent logs"
        echo ""
        echo -e "Usage: $0 [command]"
        echo ""
        echo -e "${BLUE}Quick Start:${NC}"
        echo -e "1. ${YELLOW}$0 dry-run${NC}     # Test safely first"
        echo -e "2. ${YELLOW}$0 small-test${NC}  # Send real notifications to 2 users"
        echo -e "3. ${YELLOW}$0 logs${NC}        # Check results"
        ;;
esac
