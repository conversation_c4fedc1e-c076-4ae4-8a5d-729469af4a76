flavors:
  prod:
    app:
      name: "Memorion"
      icon: "assets/images/app_launcher_icon.png"
    android:
      applicationId: "com.friend.ios"
      icon: "assets/images/app_launcher_icon.png"
      firebase:
        config: "app/android/app/src/prod/google-services.json"
    ios:
      bundleId: "com.friend-app-with-wearable.ios12.1"
      icon: "assets/images/app_launcher_icon.png"
      firebase:
        config: "app/ios/Config/Prod/GoogleService-Info.plist"
  dev:
    app:
      name: "Memorion Dev"
      icon: "assets/images/app_launcher_icon.png"
    android:
      icon: "assets/images/app_launcher_icon.png"
      applicationId: "com.friend.ios.dev"
      firebase:
        config: "app/android/app/src/dev/google-services.json"
    ios:
      bundleId: "com.friend-app-with-wearable.ios12.development.1"
      icon: "assets/images/app_launcher_icon.png"
      firebase:
        config: "app/ios/Config/Dev/GoogleService-Info.plist"
